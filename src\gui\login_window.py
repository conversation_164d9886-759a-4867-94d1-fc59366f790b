# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول الحديثة
Modern Login Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable
import math

from ..utils.colors import AppColors
from ..models.user import UserManager, User

class LoginWindow:
    """نافذة تسجيل الدخول الحديثة"""

    def __init__(self, db_manager, on_login_success: Callable[[User], None]):
        """تهيئة نافذة تسجيل الدخول"""
        self.db = db_manager
        self.on_login_success = on_login_success
        self.user_manager = UserManager(db_manager)

        # استخدام نظام الألوان الموحد
        self.colors = AppColors.get_color_scheme()

        # متغيرات السحب
        self.x = 0
        self.y = 0

        # إنشاء النافذة
        self.create_modern_window()

        # متغيرات النموذج
        self.setup_variables()

        # إعداد الواجهة الحديثة
        self.setup_modern_ui()

        # تأثير الظهور التدريجي
        self.fade_in_effect()

    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()

    def create_modern_window(self):
        """إنشاء النافذة الحديثة"""
        self.window = tk.Tk()
        self.window.title("Al Mizan")

        # تعيين الأبعاد أولاً
        self.window.geometry("450x550")
        self.window.resizable(False, False)

        # تعيين خلفية شفافة مع تدرج
        self.window.configure(bg='#f0f2f5')

        # إخفاء النافذة مؤقتاً أثناء الإعداد
        self.window.withdraw()

        # تحديث النافذة للحصول على الأبعاد الصحيحة
        self.window.update_idletasks()

        # توسيط النافذة
        self.center_window()

        # إزالة شريط العنوان الافتراضي بعد التوسيط
        self.window.overrideredirect(True)

        # إعادة توسيط النافذة بعد إزالة شريط العنوان
        self.center_window()

        # إضافة شفافية للنافذة
        try:
            self.window.attributes('-alpha', 0.95)
        except:
            pass

        # ربط أحداث السحب
        self.window.bind('<Button-1>', self.start_move)
        self.window.bind('<B1-Motion>', self.on_move)

        # إظهار النافذة بعد الإعداد الكامل
        self.window.deiconify()

        # جعل النافذة في المقدمة
        self.window.attributes('-topmost', True)
        self.window.lift()
        self.window.focus_force()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # تحديث النافذة للحصول على الأبعاد الصحيحة
        self.window.update_idletasks()

        # الحصول على أبعاد النافذة
        window_width = 450
        window_height = 550

        # الحصول على أبعاد الشاشة
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()

        # حساب الموضع المتوسط
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # تطبيق الموضع
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def setup_modern_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        # إنشاء الحاوي الرئيسي مع حدود ناعمة
        main_card = tk.Frame(self.window, bg='white', relief='flat', bd=0)
        main_card.place(x=30, y=30, width=390, height=490)

        # إضافة ظلال متدرجة للبطاقة (محاكاة)
        shadow_colors = ['#e0e0e0', '#d5d5d5', '#cccccc', '#c0c0c0']
        for i, color in enumerate(shadow_colors):
            shadow_frame = tk.Frame(self.window, bg=color, relief='flat', bd=0)
            shadow_frame.place(x=33+i, y=33+i, width=390, height=490)

        # رفع البطاقة الرئيسية فوق الظلال
        main_card.lift()

        # زر الإغلاق
        close_btn = tk.Button(main_card,
                             text="✕",
                             font=('Arial', 14, 'bold'),
                             bg='white',
                             fg='#999',
                             relief='flat',
                             bd=0,
                             width=2,
                             height=1,
                             command=self.close_window,
                             cursor='hand2')
        close_btn.place(x=360, y=10)

        # تأثيرات زر الإغلاق
        close_btn.bind('<Enter>', lambda e: close_btn.config(fg='#ff4757', bg='#f1f2f6'))
        close_btn.bind('<Leave>', lambda e: close_btn.config(fg='#999', bg='white'))

        # قسم الشعار والعنوان
        self.create_logo_section(main_card)

        # قسم نموذج تسجيل الدخول
        self.create_modern_login_form(main_card)

        # قسم المعلومات
        self.create_info_section(main_card)

    def create_logo_section(self, parent):
        """إنشاء قسم الشعار والعنوان"""
        logo_frame = tk.Frame(parent, bg='white')
        logo_frame.place(x=0, y=40, width=390, height=120)

        # شعار التطبيق مع تصميم دائري حديث
        logo_container = tk.Frame(logo_frame, bg='#4a90e2', width=80, height=80)
        logo_container.place(x=155, y=10)
        logo_container.pack_propagate(False)

        # محاولة إنشاء شكل دائري (محاكاة)
        logo_container.config(relief='flat', bd=0)

        # أيقونة الميزان مع تصميم أفضل
        logo_label = tk.Label(logo_container,
                             text="⚖",
                             font=('Arial', 36, 'bold'),
                             bg='#4a90e2',
                             fg='white')
        logo_label.place(relx=0.5, rely=0.5, anchor='center')

        # اسم التطبيق مع تصميم أنيق
        app_name = tk.Label(logo_frame,
                           text="Al Mizan",
                           font=('Arial', 26, 'bold'),
                           bg='white',
                           fg='#2c3e50')
        app_name.place(x=195, y=95, anchor='center')

    def create_modern_login_form(self, parent):
        """إنشاء نموذج تسجيل دخول حديث"""
        form_frame = tk.Frame(parent, bg='white')
        form_frame.place(x=40, y=180, width=310, height=220)

        # حقل اسم المستخدم مع حدود ناعمة
        username_frame = tk.Frame(form_frame, bg='#f8f9fa', relief='solid', bd=1, height=50)
        username_frame.place(x=0, y=20, width=310, height=50)
        username_frame.pack_propagate(False)

        # إضافة تأثير الحدود الناعمة
        username_frame.config(highlightbackground='#e9ecef', highlightthickness=1)

        # أيقونة المستخدم
        user_icon = tk.Label(username_frame,
                            text="👤",
                            font=('Arial', 16),
                            bg='#f8f9fa',
                            fg='#6c757d')
        user_icon.place(x=15, y=15)

        # حقل إدخال اسم المستخدم
        self.username_entry = tk.Entry(username_frame,
                                      textvariable=self.username_var,
                                      font=('Arial', 12),
                                      relief='flat',
                                      bd=0,
                                      bg='#f8f9fa',
                                      fg='#495057',
                                      insertbackground='#495057')
        self.username_entry.place(x=50, y=15, width=240, height=20)
        self.username_entry.insert(0, "Username")
        self.username_entry.config(fg='#adb5bd')

        # حقل كلمة المرور مع حدود ناعمة
        password_frame = tk.Frame(form_frame, bg='#f8f9fa', relief='solid', bd=1, height=50)
        password_frame.place(x=0, y=85, width=310, height=50)
        password_frame.pack_propagate(False)

        # إضافة تأثير الحدود الناعمة
        password_frame.config(highlightbackground='#e9ecef', highlightthickness=1)

        # أيقونة القفل
        lock_icon = tk.Label(password_frame,
                            text="🔒",
                            font=('Arial', 16),
                            bg='#f8f9fa',
                            fg='#6c757d')
        lock_icon.place(x=15, y=15)

        # حقل إدخال كلمة المرور
        self.password_entry = tk.Entry(password_frame,
                                      textvariable=self.password_var,
                                      font=('Arial', 12),
                                      relief='flat',
                                      bd=0,
                                      bg='#f8f9fa',
                                      fg='#495057',
                                      insertbackground='#495057')
        self.password_entry.place(x=50, y=15, width=240, height=20)
        self.password_entry.insert(0, "Password")
        self.password_entry.config(fg='#adb5bd')

        # أيقونة إظهار/إخفاء كلمة المرور
        show_password_icon = tk.Label(password_frame,
                                     text="🔒",
                                     font=('Arial', 14),
                                     bg='#f8f9fa',
                                     fg='#4a90e2',
                                     cursor='hand2')
        show_password_icon.place(x=275, y=15)

        # خانة "تذكرني"
        remember_frame = tk.Frame(form_frame, bg='white')
        remember_frame.place(x=0, y=150, width=150, height=25)

        self.remember_check = tk.Checkbutton(remember_frame,
                                           text="Remember me",
                                           variable=self.remember_var,
                                           font=('Arial', 10),
                                           bg='white',
                                           fg='#6c757d',
                                           relief='flat',
                                           bd=0)
        self.remember_check.pack(side=tk.LEFT)

        # رابط "نسيت كلمة المرور"
        forgot_link = tk.Label(form_frame,
                              text="Forgot password?",
                              font=('Arial', 10, 'underline'),
                              bg='white',
                              fg='#4a90e2',
                              cursor='hand2')
        forgot_link.place(x=200, y=152)
        forgot_link.bind('<Button-1>', lambda e: self.forgot_password())

        # زر تسجيل الدخول مع تصميم حديث
        self.login_btn = tk.Button(form_frame,
                                  text="LOG IN",
                                  font=('Arial', 14, 'bold'),
                                  bg='#4a90e2',
                                  fg='white',
                                  relief='flat',
                                  bd=0,
                                  width=25,
                                  height=2,
                                  command=self.login,
                                  cursor='hand2')
        self.login_btn.place(x=55, y=185, width=200, height=45)

        # إضافة تأثير الظل للزر (محاكاة)
        shadow_btn = tk.Frame(form_frame, bg='#d0d0d0', height=45)
        shadow_btn.place(x=57, y=187, width=200, height=45)
        self.login_btn.lift()

        # إعداد تأثيرات الحقول
        self.setup_entry_effects()

        # ربط الأحداث
        self.username_var.trace('w', self.check_login_button_state)
        self.password_var.trace('w', self.check_login_button_state)
        self.window.bind('<Return>', lambda e: self.login() if self.login_btn['state'] == 'normal' else None)

        # تأثيرات زر تسجيل الدخول المحسنة
        def on_login_enter(e):
            if self.login_btn['state'] == 'normal':
                self.login_btn.config(bg='#357abd', relief='raised')

        def on_login_leave(e):
            if self.login_btn['state'] == 'normal':
                self.login_btn.config(bg='#4a90e2', relief='flat')

        self.login_btn.bind('<Enter>', on_login_enter)
        self.login_btn.bind('<Leave>', on_login_leave)

    def create_info_section(self, parent):
        """إنشاء قسم المعلومات"""
        info_frame = tk.Frame(parent, bg='#f8f9fa', relief='flat', bd=1)
        info_frame.place(x=40, y=420, width=310, height=50)
        info_frame.config(highlightbackground='#e9ecef', highlightthickness=1)

        # أيقونة المعلومات
        info_icon = tk.Label(info_frame,
                            text="💡",
                            font=('Arial', 14),
                            bg='#f8f9fa',
                            fg='#ffc107')
        info_icon.place(x=10, y=15)

        # نص المعلومات مع تصميم أفضل
        info_text = tk.Label(info_frame,
                            text="Default: admin / admin123",
                            font=('Arial', 11),
                            bg='#f8f9fa',
                            fg='#6c757d')
        info_text.place(x=40, y=16)

    def setup_entry_effects(self):
        """إعداد تأثيرات حقول الإدخال"""
        # الحصول على إطارات الحقول للتأثيرات
        username_frame = self.username_entry.master
        password_frame = self.password_entry.master

        # تأثيرات حقل اسم المستخدم
        def on_username_focus_in(event):
            if self.username_entry.get() == "Username":
                self.username_entry.delete(0, tk.END)
                self.username_entry.config(fg='#495057')
            # تغيير لون الحدود عند التركيز
            username_frame.config(highlightbackground='#4a90e2', highlightthickness=2)

        def on_username_focus_out(event):
            if not self.username_entry.get():
                self.username_entry.insert(0, "Username")
                self.username_entry.config(fg='#adb5bd')
            # إعادة لون الحدود الطبيعي
            username_frame.config(highlightbackground='#e9ecef', highlightthickness=1)

        # تأثيرات حقل كلمة المرور
        def on_password_focus_in(event):
            if self.password_entry.get() == "Password":
                self.password_entry.delete(0, tk.END)
                self.password_entry.config(fg='#495057', show='*')
            # تغيير لون الحدود عند التركيز
            password_frame.config(highlightbackground='#4a90e2', highlightthickness=2)

        def on_password_focus_out(event):
            if not self.password_entry.get():
                self.password_entry.insert(0, "Password")
                self.password_entry.config(fg='#adb5bd', show='')
            # إعادة لون الحدود الطبيعي
            password_frame.config(highlightbackground='#e9ecef', highlightthickness=1)

        self.username_entry.bind('<FocusIn>', on_username_focus_in)
        self.username_entry.bind('<FocusOut>', on_username_focus_out)
        self.password_entry.bind('<FocusIn>', on_password_focus_in)
        self.password_entry.bind('<FocusOut>', on_password_focus_out)

    def check_login_button_state(self, *args):
        """فحص حالة زر تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        # تجاهل النصوص الافتراضية
        if username == "Username":
            username = ""
        if password == "Password":
            password = ""

        if username and password:
            self.login_btn.config(state='normal', bg='#4a90e2', cursor='hand2')
        else:
            self.login_btn.config(state='disabled', bg='#adb5bd', cursor='arrow')

    def close_window(self):
        """إغلاق النافذة"""
        self.window.destroy()

    def login(self):
        """تسجيل الدخول"""
        if self.login_btn['state'] != 'normal':
            return

        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        # تجاهل النصوص الافتراضية
        if username == "Username":
            username = ""
        if password == "Password":
            password = ""

        if not username or not password:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # تغيير حالة الزر
        self.login_btn.config(text="⏳ Logging in...", state='disabled', bg='#ffc107')
        self.window.update()

        try:
            user = self.user_manager.authenticate(username, password)
            if user:
                self.login_btn.config(text="✅ Success!", bg='#28a745')
                self.window.update()
                self.window.after(500, lambda: self._complete_login(user))
            else:
                self.login_btn.config(text="❌ Failed", bg='#dc3545')
                self.window.update()
                self.window.after(1500, self._reset_login_button)
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
        except Exception as e:
            self.login_btn.config(text="❌ Error", bg='#dc3545')
            self.window.update()
            self.window.after(1500, self._reset_login_button)
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {str(e)}")

    def _reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_btn.config(text="LOG IN", state='normal', bg='#4a90e2')

    def _complete_login(self, user):
        """إكمال عملية تسجيل الدخول"""
        # تأثير الاختفاء التدريجي
        self._fade_out_and_close(user)

    def _fade_out_and_close(self, user):
        """تأثير الاختفاء التدريجي وإغلاق النافذة"""
        try:
            self.window.attributes('-alpha', 0.8)
            self.window.after(50, lambda: self._fade_out_step(0.8, user))
        except:
            self._close_and_callback(user)

    def _fade_out_step(self, alpha, user):
        """خطوة في تأثير الاختفاء التدريجي"""
        try:
            alpha -= 0.1
            if alpha <= 0.0:
                self._close_and_callback(user)
                return
            self.window.attributes('-alpha', alpha)
            self.window.after(30, lambda: self._fade_out_step(alpha, user))
        except:
            self._close_and_callback(user)

    def _close_and_callback(self, user):
        """إغلاق النافذة واستدعاء دالة النجاح"""
        try:
            # استدعاء callback أولاً
            self.on_login_success(user)
        except Exception as e:
            print(f"خطأ في callback: {e}")
        finally:
            # تدمير النافذة في النهاية
            try:
                if self.window and self.window.winfo_exists():
                    self.window.destroy()
            except:
                pass

    def fade_in_effect(self):
        """تأثير الظهور التدريجي"""
        try:
            self.window.attributes('-alpha', 0.0)
            self._fade_in_step(0.0)
        except:
            pass

    def _fade_in_step(self, alpha):
        """خطوة في تأثير الظهور"""
        try:
            alpha += 0.05
            if alpha >= 0.95:
                alpha = 0.95
            self.window.attributes('-alpha', alpha)
            if alpha < 0.95:
                self.window.after(30, lambda: self._fade_in_step(alpha))
        except:
            pass

    def start_move(self, event):
        """بداية سحب النافذة"""
        self.x = event.x
        self.y = event.y

    def on_move(self, event):
        """تحريك النافذة"""
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.window.winfo_x() + deltax
        y = self.window.winfo_y() + deltay
        self.window.geometry(f"+{x}+{y}")

    def forgot_password(self):
        """نسيان كلمة المرور"""
        messagebox.showinfo("نسيان كلمة المرور",
                           "يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور")

    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

    def show(self):
        """عرض النافذة"""
        self.window.deiconify()
        self.window.lift()
        self.window.focus_force()

    def hide(self):
        """إخفاء النافذة"""
        self.window.withdraw()

    def destroy(self):
        """إغلاق النافذة"""
        self.window.destroy()